#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import sys
from datetime import datetime

def export_popular_notes(min_likes=1000, output_file='popular_notes.txt'):
    """
    从数据库中导出点赞数大于指定值的笔记
    
    Args:
        min_likes: 最小点赞数，默认1000
        output_file: 输出文件名
    """
    connection = None
    cursor = None
    
    try:
        MYSQL_CONFIG = {
        'host': 'rm-2ze41522tus42rf3eao.mysql.rds.aliyuncs.com',
        'user': 'max_db_backend',
        'password': '171@aBa#uuDhac%AxaqoY81',
        'database': 'xhs_note_mobile',
        'charset': 'utf8mb4'
    }
    
        # 连接数据库
        connection = pymysql.connect(**MYSQL_CONFIG)
        cursor = connection.cursor()
        
        # 查询点赞数大于1000且描述不少于50字的笔记
        query = """
        SELECT title, `desc`
        FROM xhs_notes_enhanced
        WHERE liked_count > %s
        AND CHAR_LENGTH(`desc`) >= 10
        ORDER BY liked_count DESC
        """
        
        cursor.execute(query, (min_likes,))
        results = cursor.fetchall()
        
        print(f"找到 {len(results)} 条点赞数大于 {min_likes} 的笔记")
        
        # 定义标点符号集合（中英文）
        punctuation_marks = {'。', '！', '？', '…', '.', '!', '?', '~', '～', '）', ')', '"', '"', ''', ''', '\'', '"','#'}
        
        # 导出到文本文件
        filtered_count = 0
        with open(output_file, 'w', encoding='utf-8') as f:
            for idx, (title, desc) in enumerate(results, 1):
                # 检查描述是否以标点符号结尾
                if desc and desc.strip() and desc.strip()[-1] not in punctuation_marks:
                    continue  # 跳过不以标点符号结尾的描述
                
                filtered_count += 1
                if title:
                    f.write(f"{title}\n")
                if desc:
                    f.write(f"{desc}\n")
                f.write("\n" + "-" * 80 + "\n\n")
        
        print(f"导出成功！共筛选出 {filtered_count} 条完整的笔记（原始 {len(results)} 条）")
        print(f"文件保存为: {output_file}")
        
    except pymysql.Error as e:
        print(f"数据库错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    # 使用示例
    # 1. 使用默认参数（点赞数>1000，输出到popular_notes.txt）
    export_popular_notes(min_likes=10)
    
    # 2. 自定义参数
    # export_popular_notes(min_likes=2000, output_file='high_popular_notes.txt')